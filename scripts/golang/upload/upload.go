package main

import (
	"fmt"
	"io"
	"os"
	"os/exec"
	"path/filepath"
	"sync"
	"time"

	"github.com/cheggaaa/pb/v3"
	"github.com/pkg/sftp"
	"github.com/sirupsen/logrus"
	"golang.org/x/crypto/ssh"
	"gopkg.in/yaml.v2"
)

var (
	log           = logrus.New()
	totalBytes    int64
	uploadedBytes int64
	bufPool       = sync.Pool{
		New: func() interface{} {
			return make([]byte, 32*1024) // 32KB buffer
		},
	}
)

// 配置结构体
type Config struct {
	SSH struct {
		Host         string `yaml:"host"`
		User         string `yaml:"user"`
		Password     string `yaml:"password"`
		Port         int    `yaml:"port"`
		FrontendPath string `yaml:"frontend_path"`
		BackendPaths struct {
			API     string `yaml:"api"`
			Migrate string `yaml:"migrate"`
			Task    string `yaml:"task"`
			Track   string `yaml:"track"`
		} `yaml:"backend_paths"`
	} `yaml:"ssh"`

	Local struct {
		FrontendDist string `yaml:"frontend_dist"`
		BackendBin   string `yaml:"backend_bin"`
		BackupDir    string `yaml:"backup_dir"`
	} `yaml:"local"`

	Retry struct {
		MaxAttempts int           `yaml:"max_attempts"`
		Delay       time.Duration `yaml:"delay"`
	} `yaml:"retry"`

	Upload struct {
		Workers    int `yaml:"workers"`
		BufferSize int `yaml:"buffer_size"`
	} `yaml:"upload"`
}

var config Config

type FileInfo struct {
	Path     string
	Info     os.FileInfo
	RelPath  string
	ModTime  time.Time
	Checksum string
}

func init() {
	// 配置日志
	log.SetFormatter(&logrus.TextFormatter{
		FullTimestamp: true,
	})

	// 创建日志文件
	logFile, err := os.OpenFile("upload.log", os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		log.Fatal("无法创建日志文件: ", err)
	}
	log.SetOutput(logFile)

	// 加载配置文件
	configData, err := os.ReadFile("./upload/upload_config.yaml")
	if err != nil {
		log.Fatal("无法读取配置文件: ", err)
	}

	if err := yaml.Unmarshal(configData, &config); err != nil {
		log.Fatal("无法解析配置文件: ", err)
	}
}

func build() error {
	log.Info("开始构建项目...")
	cmd := exec.Command("make", "build")
	cmd.Dir = "../../"

	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("项目构建失败: %v\n%s", err, output)
	}
	log.Info("项目构建完成")
	return nil
}

func main() {
	// 构建项目
	if err := build(); err != nil {
		log.Fatal(err)
	}

	// 上传前端文件
	log.Info("开始上传前端文件...")
	if err := uploadFrontend(config.Local.FrontendDist, config.SSH.FrontendPath); err != nil {
		log.Fatal("上传前端文件失败: ", err)
	}
	log.Info("前端文件上传完成")

	// 上传后端文件
	log.Info("开始上传后端文件...")
	// 分别上传不同的后端二进制文件到对应目录
	backendFiles := map[string]string{
		"api":     config.SSH.BackendPaths.API,
		"migrate": config.SSH.BackendPaths.Migrate,
		"task":    config.SSH.BackendPaths.Task,
		"track":   config.SSH.BackendPaths.Track,
	}

	for binary, remotePath := range backendFiles {
		localBinary := filepath.Join(config.Local.BackendBin, binary)
		if err := uploadBackend(localBinary, remotePath); err != nil {
			log.Fatalf("上传后端%s文件失败: %v", binary, err)
		}
		log.Infof("后端%s文件上传完成", binary)
	}
}

func uploadBackend(localPath, remotePath string) error {
	// 确保remotePath是目录路径
	remoteDir := remotePath
	// 获取本地文件名
	localFileName := filepath.Base(localPath)
	// 创建临时文件路径，将文件放在目标目录下
	tempRemotePath := filepath.Join(remoteDir, localFileName+".tmp_"+time.Now().Format("20060102_150405"))
	// 最终的目标文件路径
	finalRemotePath := filepath.Join(remoteDir, localFileName)

	// 连接SSH服务器
	sshClient, err := connectSSH()
	if err != nil {
		return fmt.Errorf("SSH连接失败: %v", err)
	}
	defer sshClient.Close()

	// 创建SFTP客户端
	sftpClient, err := sftp.NewClient(sshClient)
	if err != nil {
		return fmt.Errorf("SFTP客户端创建失败: %v", err)
	}
	defer sftpClient.Close()

	// 打开本地文件
	localFile, err := os.Open(localPath)
	if err != nil {
		return fmt.Errorf("打开本地文件失败: %v", err)
	}
	defer localFile.Close()

	// 创建临时远程文件
	remoteFile, err := sftpClient.Create(tempRemotePath)
	if err != nil {
		return fmt.Errorf("创建远程文件失败: %v", err)
	}
	defer remoteFile.Close()

	// 创建进度条
	fileInfo, err := localFile.Stat()
	if err != nil {
		return fmt.Errorf("获取文件信息失败: %v", err)
	}

	bar := pb.Full.Start64(fileInfo.Size())
	bar.Set(pb.Bytes, true)
	bar.SetWidth(80)                                                                            // 设置固定宽度
	bar.SetRefreshRate(time.Second)                                                             // 设置刷新频率为1秒
	bar.Set(pb.Terminal, false)                                                                 // 禁用终端检测
	bar.Set(pb.Static, false)                                                                   // 启用动态更新
	bar.SetTemplateString(`\r上传进度: {{bar . }} {{percent . }} {{speed . }} {{counters . }}`) // 使用\r实现单行更新

	// 从内存池获取缓冲区
	buf := bufPool.Get().([]byte)
	defer bufPool.Put(buf)

	// 复制文件内容
	for {
		n, err := localFile.Read(buf)
		if n > 0 {
			_, writeErr := remoteFile.Write(buf[:n])
			if writeErr != nil {
				return fmt.Errorf("写入远程文件失败: %v", writeErr)
			}
			bar.Add(n)
		}
		if err != nil {
			if err != io.EOF {
				return fmt.Errorf("读取本地文件失败: %v", err)
			}
			break
		}
	}

	bar.Finish()
	fmt.Println("tempRemotePath:", tempRemotePath)
	// 设置文件权限
	if err := sftpClient.Chmod(tempRemotePath, 0755); err != nil {
		return fmt.Errorf("设置文件权限失败: %v", err)
	}

	// 备份当前文件
	backupPath := filepath.Join(remoteDir, localFileName+".bak_"+time.Now().Format("20060102_150405"))
	if _, err := sftpClient.Stat(finalRemotePath); err == nil {
		if err := sftpClient.Rename(finalRemotePath, backupPath); err != nil {
			return fmt.Errorf("创建备份失败: %v", err)
		}
	}

	// 替换文件
	fmt.Println("tempRemotePath, finalRemotePath", tempRemotePath, finalRemotePath)
	if err := sftpClient.Rename(tempRemotePath, finalRemotePath); err != nil {
		// 如果替换失败，尝试恢复备份
		if restoreErr := sftpClient.Rename(backupPath, finalRemotePath); restoreErr != nil {
			log.WithFields(logrus.Fields{
				"error": restoreErr,
			}).Error("恢复备份失败")
			return fmt.Errorf("部署失败且无法恢复: %v, 恢复错误: %v", err, restoreErr)
		}
		return fmt.Errorf("部署失败，已恢复备份: %v", err)
	}

	// 删除备份文件
	if err := sftpClient.Remove(backupPath); err != nil {
		log.WithFields(logrus.Fields{
			"path":  backupPath,
			"error": err,
		}).Warning("删除备份文件失败")
	}

	return nil
}

func uploadFrontend(localPath, remotePath string) error {
	// 创建临时目录
	tempRemotePath := filepath.Join(filepath.Dir(remotePath), ".tmp_upload_"+time.Now().Format("20060102_150405"))

	// 在函数结束时清理临时目录
	defer func() {
		// 连接SSH服务器
		sshClient, err := connectSSH()
		if err == nil {
			defer sshClient.Close()
			// 创建SFTP客户端
			sftpClient, err := sftp.NewClient(sshClient)
			if err == nil {
				defer sftpClient.Close()
				// 清理临时目录
				if err := sftpClient.RemoveDirectory(tempRemotePath); err != nil {
					log.WithFields(logrus.Fields{
						"path": tempRemotePath,
						"warn": err,
					}).Warning("清理临时目录失败")
				}
			}
		}
	}()

	// 连接SSH服务器
	sshClient, err := connectSSH()
	if err != nil {
		return fmt.Errorf("SSH连接失败: %v", err)
	}
	defer sshClient.Close()

	// 创建SFTP客户端
	sftpClient, err := sftp.NewClient(sshClient)
	if err != nil {
		return fmt.Errorf("SFTP客户端创建失败: %v", err)
	}
	defer sftpClient.Close()

	// 收集所有文件信息并计算总大小
	var files []FileInfo
	totalBytes = 0
	uploadedBytes = 0

	err = filepath.Walk(localPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if !info.IsDir() {
			relPath, err := filepath.Rel(localPath, path)
			if err != nil {
				return err
			}

			checksum, err := calculateChecksum(path)
			if err != nil {
				return err
			}

			files = append(files, FileInfo{
				Path:     path,
				Info:     info,
				RelPath:  relPath,
				ModTime:  info.ModTime(),
				Checksum: checksum,
			})
			totalBytes += info.Size()
		}
		return nil
	})

	if err != nil {
		return fmt.Errorf("收集文件信息失败: %v", err)
	}

	// 创建进度条
	bar := pb.Full.Start64(totalBytes)
	bar.Set(pb.Bytes, true)
	bar.SetWidth(80)                                                                            // 设置固定宽度
	bar.SetRefreshRate(time.Second)                                                             // 设置刷新频率为1秒
	bar.Set(pb.Terminal, false)                                                                 // 禁用终端检测
	bar.Set(pb.Static, false)                                                                   // 启用动态更新
	bar.SetTemplateString(`\r上传进度: {{bar . }} {{percent . }} {{speed . }} {{counters . }}`) // 使用\r实现单行更新

	// 创建工作池
	jobs := make(chan FileInfo, len(files))
	errors := make(chan error, len(files))
	wg := sync.WaitGroup{}

	// 启动工作协程
	workerCount := config.Upload.Workers
	for i := 0; i < workerCount; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for file := range jobs {
				remoteFilePath := filepath.Join(tempRemotePath, file.RelPath)
				remoteDir := filepath.Dir(remoteFilePath)

				// 创建远程目录
				if err := sftpClient.MkdirAll(remoteDir); err != nil {
					errors <- fmt.Errorf("创建远程目录失败 %s: %v", remoteDir, err)
					continue
				}

				// 打开本地文件
				localFile, err := os.Open(file.Path)
				if err != nil {
					errors <- fmt.Errorf("打开本地文件失败 %s: %v", file.Path, err)
					continue
				}

				// 创建远程文件
				remoteFile, err := sftpClient.Create(remoteFilePath)
				if err != nil {
					localFile.Close()
					errors <- fmt.Errorf("创建远程文件失败 %s: %v", remoteFilePath, err)
					continue
				}

				// 从内存池获取缓冲区
				buf := bufPool.Get().([]byte)
				defer bufPool.Put(buf)

				// 复制文件内容
				for {
					n, err := localFile.Read(buf)
					if n > 0 {
						_, writeErr := remoteFile.Write(buf[:n])
						if writeErr != nil {
							errors <- fmt.Errorf("写入远程文件失败 %s: %v", remoteFilePath, writeErr)
							break
						}
						uploadedBytes += int64(n)
						bar.SetCurrent(uploadedBytes)
					}
					if err != nil {
						if err != io.EOF {
							errors <- fmt.Errorf("读取本地文件失败 %s: %v", file.Path, err)
						}
						break
					}
				}

				localFile.Close()
				remoteFile.Close()

				// 设置文件权限和修改时间
				if err := sftpClient.Chmod(remoteFilePath, file.Info.Mode()); err != nil {
					log.WithFields(logrus.Fields{
						"path":  remoteFilePath,
						"error": err,
					}).Warning("设置文件权限失败")
				}
			}
		}()
	}

	// 分发任务
	for _, file := range files {
		jobs <- file
	}
	close(jobs)

	// 等待所有工作完成
	wg.Wait()
	close(errors)
	bar.Finish()

	// 检查是否有错误
	for err := range errors {
		log.Error(err)
		return fmt.Errorf("上传过程中发生错误: %v", err)
	}

	// 备份当前目录
	backupPath := remotePath + ".bak_" + time.Now().Format("20060102_150405")
	if _, err := sftpClient.Stat(remotePath); err == nil {
		if err := sftpClient.Rename(remotePath, backupPath); err != nil {
			return fmt.Errorf("创建备份失败: %v", err)
		}
	}

	// 替换目录
	if err := sftpClient.Rename(tempRemotePath, remotePath); err != nil {
		// 如果替换失败，尝试恢复备份
		if restoreErr := sftpClient.Rename(backupPath, remotePath); restoreErr != nil {
			log.WithFields(logrus.Fields{
				"error": restoreErr,
			}).Error("恢复备份失败")
			return fmt.Errorf("部署失败且无法恢复: %v, 恢复错误: %v", err, restoreErr)
		}
		return fmt.Errorf("部署失败，已恢复备份: %v", err)
	}

	// 删除备份目录
	if err := sftpClient.RemoveDirectory(backupPath); err != nil {
		log.WithFields(logrus.Fields{
			"path":  backupPath,
			"error": err,
		}).Warning("删除备份目录失败")
	}

	return nil
}

func connectSSH() (*ssh.Client, error) {
	var client *ssh.Client
	var err error

	// 配置SSH客户端
	sshConfig := &ssh.ClientConfig{
		User: config.SSH.User,
		Auth: []ssh.AuthMethod{
			ssh.Password(config.SSH.Password),
		},
		HostKeyCallback: ssh.InsecureIgnoreHostKey(),
		Timeout:         5 * time.Second,
	}

	host := fmt.Sprintf("%s:%d", config.SSH.Host, config.SSH.Port)

	for attempt := 1; attempt <= config.Retry.MaxAttempts; attempt++ {
		client, err = ssh.Dial("tcp", host, sshConfig)
		if err == nil {
			return client, nil
		}

		log.WithFields(logrus.Fields{
			"attempt": attempt,
			"error":   err,
		}).Warning("SSH连接失败，准备重试")

		if attempt < config.Retry.MaxAttempts {
			time.Sleep(config.Retry.Delay)
		}
	}

	return nil, fmt.Errorf("在%d次尝试后仍无法连接SSH: %v", config.Retry.MaxAttempts, err)
}

func createBackup(sftpClient *sftp.Client, remotePath string) error {
	// 检查远程路径是否存在
	_, err := sftpClient.Stat(remotePath)
	if err != nil {
		if os.IsNotExist(err) {
			return nil // 如果目录不存在，不需要备份
		}
		return err
	}

	// 创建备份目录
	backupPath := filepath.Join(config.Local.BackupDir, time.Now().Format("20060102_150405"))
	if err := os.MkdirAll(backupPath, 0755); err != nil {
		return err
	}

	return nil
}

func calculateChecksum(path string) (string, error) {
	// 简单实现：使用文件大小和修改时间作为校验和
	info, err := os.Stat(path)
	if err != nil {
		return "", err
	}
	return fmt.Sprintf("%d-%d", info.Size(), info.ModTime().Unix()), nil
}
