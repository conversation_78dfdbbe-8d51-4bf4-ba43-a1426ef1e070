package main

import (
	"bonusearned/config"
	blogentity "bonusearned/domain/blog/entity"
	clickrecordentity "bonusearned/domain/clickrecord/entity"
	couponentity "bonusearned/domain/coupon/entity"
	merchantentity "bonusearned/domain/merchant/entity"
	orderentity "bonusearned/domain/order/entity"
	userentity "bonusearned/domain/user/entity"
	withdrawalentity "bonusearned/domain/withdrawal/entity"
	"bonusearned/infra/database"
	"flag"
	"log"
	"os"
	"path/filepath"
	"strings"

	"github.com/joho/godotenv"
	"gorm.io/gorm"
)

func main() {
	// 获取环境配置的优先级：
	// 1. 命令行参数
	// 2. 环境变量
	// 3. .env 文件
	// 4. 默认值

	var env string
	flag.StringVar(&env, "env", "", "environment (local, test, live)")
	flag.Parse()

	// 如果没有通过命令行指定环境，则尝试从环境变量获取
	if env == "" {
		env = os.Getenv("APP_ENV")
	}

	// 如果环境变量也没有设置，则尝试加载 .env 文件
	if env == "" {
		// 获取项目根目录
		rootDir := findProjectRoot()

		// 加载 env 文件
		if err := godotenv.Load(filepath.Join(rootDir, "env")); err != nil {
			log.Printf("Warning: Error loading env file: %v", err)
		}

		env = os.Getenv("APP_ENV")
	}

	// 如果所有方式都没有设置环境，则使用默认值
	if env == "" {
		env = "local"
	}

	// 加载配置
	cfg, err := config.LoadConfig(env)
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 连接数据库
	postgres, err := database.NewPostgresDB(cfg.Postgres)
	if err != nil {
		log.Fatalf("Failed to connect to database： %v", err)
	}

	// 运行迁移（忽略约束删除错误）
	log.Println("Running AutoMigrate...")
	err = postgres.AutoMigrate(
		&blogentity.Blog{},
		&clickrecordentity.ClickRecord{},
		&couponentity.Coupon{},
		&merchantentity.Merchant{},
		&merchantentity.Category{},
		&merchantentity.CashbackRule{},
		&merchantentity.Country{},
		&orderentity.Order{},
		&userentity.User{},
		&withdrawalentity.Withdrawal{},
	)
	if err != nil {
		// 检查是否是约束不存在的错误，如果是则忽略
		if isConstraintNotExistError(err) {
			log.Printf("Warning: Constraint error ignored: %v", err)
		} else {
			log.Fatalf("Failed to run migrations: %v", err)
		}
	}

	// 处理CashbackRule表的特殊约束和索引
	err = handleCashbackRuleConstraints(postgres)
	if err != nil {
		log.Fatalf("Failed to handle CashbackRule constraints: %v", err)
	}

	log.Println("Migrations completed successfully")
}

// handleCashbackRuleConstraints 处理CashbackRule表的特殊约束和索引
func handleCashbackRuleConstraints(db *gorm.DB) error {
	log.Println("Handling CashbackRule constraints...")

	// 1. 删除旧的唯一索引（如果存在）
	if db.Migrator().HasIndex(&merchantentity.CashbackRule{}, "idx_user_merchant") {
		err := db.Migrator().DropIndex(&merchantentity.CashbackRule{}, "idx_user_merchant")
		if err != nil {
			log.Printf("Warning: Failed to drop old index idx_user_merchant: %v", err)
		} else {
			log.Println("Dropped old index: idx_user_merchant")
		}
	}

	// 2. 更新现有的全局规则数据，确保 merchant_id 为 0
	result := db.Model(&merchantentity.CashbackRule{}).
		Where("is_global = ?", true).
		Update("merchant_id", 0)
	if result.Error != nil {
		log.Printf("Warning: Failed to update global rules merchant_id: %v", result.Error)
	} else {
		log.Printf("Updated %d global rules to set merchant_id = 0", result.RowsAffected)
	}

	// 3. 创建用户全局规则的唯一索引（每个用户只能有一个全局规则）
	err := db.Exec(`
		CREATE UNIQUE INDEX IF NOT EXISTS idx_user_global
		ON cashback_rules (user_id, is_global)
		WHERE is_global = true AND status = 1
	`).Error
	if err != nil {
		log.Printf("Warning: Failed to create idx_user_global: %v", err)
	} else {
		log.Println("Created unique index: idx_user_global")
	}

	// 4. 创建用户特定商家规则的唯一索引（每个用户每个商家只能有一个规则）
	err = db.Exec(`
		CREATE UNIQUE INDEX IF NOT EXISTS idx_user_merchant_specific
		ON cashback_rules (user_id, merchant_id)
		WHERE is_global = false AND status = 1 AND merchant_id > 0
	`).Error
	if err != nil {
		log.Printf("Warning: Failed to create idx_user_merchant_specific: %v", err)
	} else {
		log.Println("Created unique index: idx_user_merchant_specific")
	}

	// 5. 添加检查约束（全局规则merchant_id必须为0，特定商家规则merchant_id必须大于0）
	err = db.Exec(`
		ALTER TABLE cashback_rules
		DROP CONSTRAINT IF EXISTS chk_global_merchant_id
	`).Error
	if err != nil {
		log.Printf("Warning: Failed to drop old constraint chk_global_merchant_id: %v", err)
	}

	err = db.Exec(`
		ALTER TABLE cashback_rules
		ADD CONSTRAINT chk_global_merchant_id
		CHECK (
			(is_global = true AND merchant_id = 0) OR
			(is_global = false AND merchant_id > 0)
		)
	`).Error
	if err != nil {
		log.Printf("Warning: Failed to create constraint chk_global_merchant_id: %v", err)
	} else {
		log.Println("Created check constraint: chk_global_merchant_id")
	}

	// 6. 添加时间范围检查约束
	err = db.Exec(`
		ALTER TABLE cashback_rules
		DROP CONSTRAINT IF EXISTS chk_time_range
	`).Error
	if err != nil {
		log.Printf("Warning: Failed to drop old constraint chk_time_range: %v", err)
	}

	err = db.Exec(`
		ALTER TABLE cashback_rules
		ADD CONSTRAINT chk_time_range
		CHECK (
			(start_time IS NULL AND end_time IS NULL) OR
			(start_time IS NOT NULL AND end_time IS NOT NULL AND start_time < end_time) OR
			(start_time IS NULL AND end_time IS NOT NULL) OR
			(start_time IS NOT NULL AND end_time IS NULL)
		)
	`).Error
	if err != nil {
		log.Printf("Warning: Failed to create constraint chk_time_range: %v", err)
	} else {
		log.Println("Created check constraint: chk_time_range")
	}

	// 7. 添加返现比例范围检查约束
	err = db.Exec(`
		ALTER TABLE cashback_rules
		DROP CONSTRAINT IF EXISTS chk_cashback_rate_range
	`).Error
	if err != nil {
		log.Printf("Warning: Failed to drop old constraint chk_cashback_rate_range: %v", err)
	}

	err = db.Exec(`
		ALTER TABLE cashback_rules
		ADD CONSTRAINT chk_cashback_rate_range
		CHECK (cashback_rate >= 0 AND cashback_rate <= 1)
	`).Error
	if err != nil {
		log.Printf("Warning: Failed to create constraint chk_cashback_rate_range: %v", err)
	} else {
		log.Println("Created check constraint: chk_cashback_rate_range")
	}

	log.Println("CashbackRule constraints handling completed")
	return nil
}

// isConstraintNotExistError 检查是否是约束不存在的错误
func isConstraintNotExistError(err error) bool {
	if err == nil {
		return false
	}
	errStr := strings.ToLower(err.Error())
	return strings.Contains(errStr, "constraint") &&
		(strings.Contains(errStr, "does not exist") ||
			strings.Contains(errStr, "不存在"))
}

// findProjectRoot 查找项目根目录
func findProjectRoot() string {
	// 从当前目录开始向上查找，直到找到包含 .env 文件的目录
	dir, err := os.Getwd()
	if err != nil {
		return ""
	}

	for {
		if _, err := os.Stat(filepath.Join(dir, "env")); err == nil {
			return dir
		}

		parent := filepath.Dir(dir)
		if parent == dir {
			return ""
		}
		dir = parent
	}
}
