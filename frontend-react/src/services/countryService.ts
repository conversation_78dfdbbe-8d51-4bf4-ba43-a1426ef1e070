import api from '../lib/api'

interface Country {
  id: number
  name: string
  code: string
  flag: string
  merchant_count: number
  status: number
}

interface CountryListResponse {
  total: number
  page: number
  page_size: number
  country_list: Country[]
}

class CountryService {
  private countryCodeToIdMap: Map<string, number> = new Map()
  private isInitialized = false

  // 初始化国家代码到ID的映射
  async initializeCountryMapping(): Promise<void> {
    if (this.isInitialized) {
      return
    }

    try {
      const response = await api.get<CountryListResponse>('/countries', {
        params: {
          page: 1,
          page_size: 100
        }
      })

      if (response.country_list) {
        response.country_list.forEach(country => {
          this.countryCodeToIdMap.set(country.code.toUpperCase(), country.id)
        })
        this.isInitialized = true
      }
    } catch (error) {
      console.error('Failed to initialize country mapping:', error)
      // 设置默认映射（美国）
      this.countryCodeToIdMap.set('US', 1)
      this.isInitialized = true
    }
  }

  // 根据国家代码获取国家ID
  async getCountryIdByCode(countryCode: string): Promise<number | null> {
    await this.initializeCountryMapping()
    return this.countryCodeToIdMap.get(countryCode.toUpperCase()) || null
  }

  // 获取所有国家映射
  async getAllCountryMappings(): Promise<Map<string, number>> {
    await this.initializeCountryMapping()
    return new Map(this.countryCodeToIdMap)
  }

  // 重新初始化映射（用于刷新数据）
  async refreshCountryMapping(): Promise<void> {
    this.isInitialized = false
    this.countryCodeToIdMap.clear()
    await this.initializeCountryMapping()
  }
}

// 创建单例实例
const countryService = new CountryService()

export default countryService
